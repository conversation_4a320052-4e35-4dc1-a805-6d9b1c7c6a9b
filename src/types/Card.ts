export interface OracleCard {
  title: string;
  suite: string;
  poetic_meaning: string;
  affirmation: string;
  image: string;
}

export interface DrawnCard extends OracleCard {
  id: string;
  isRevealed: boolean;
  position: number;
}

export type SpreadType = 'single' | 'three';

export interface ReadingState {
  phase: 'question' | 'shuffle' | 'selection' | 'drawing' | 'drawing-cards' | 'reading' | 'complete';
  spreadType: SpreadType | null;
  drawnCards: DrawnCard[];
  isShuffling: boolean;
  deckShuffled: boolean;
}
