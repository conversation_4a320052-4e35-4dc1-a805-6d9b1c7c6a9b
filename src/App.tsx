import { Container, Typography, Box } from '@mui/material';
import { OracleReading } from './components/OracleReading';
import "./App.css";

function App() {
  return (
    <div className="app">
      <Box component="header" className="app-header" sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="h1" component="h1" sx={{ mb: 2 }}>
          The Descent of Falling Bird
        </Typography>
        <Typography variant="h6" component="p" sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
          Sacred Oracle of Shadow Work & Moonlit Wisdom
        </Typography>
      </Box>

      <Container component="main" className="app-main" maxWidth="xl" sx={{ py: 4 }}>
        <OracleReading />
      </Container>
    </div>
  );
}

export default App;
