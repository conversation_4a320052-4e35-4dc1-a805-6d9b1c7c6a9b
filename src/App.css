:root {
  /* Cosmic-Tribal Fusion Typography */
  --font-title: '<PERSON><PERSON><PERSON>', serif;
  --font-body: '<PERSON><PERSON>', serif;

  /* Dark & Dreamy Color Palette */
  --cosmic-void: #0a0a0f;
  --shadow-deep: #1a1a2e;
  --tribal-earth: #2d1b3d;
  --sacred-purple: #4a2c5a;
  --moonlight-silver: #c9b8d4;
  --feather-gold: #d4af37;
  --bioluminescent-blue: #64b5f6;
  --ethereal-white: #f5f3f0;
  --mystical-glow: rgba(212, 175, 55, 0.3);

  font-family: var(--font-body);
  font-size: 16px;
  line-height: 1.7;
  font-weight: 400;

  color: var(--moonlight-silver);
  background:
    radial-gradient(ellipse at top, var(--tribal-earth) 0%, var(--shadow-deep) 40%, var(--cosmic-void) 100%),
    linear-gradient(135deg, var(--cosmic-void) 0%, var(--shadow-deep) 50%, var(--tribal-earth) 100%);
  min-height: 100vh;
  overflow-x: hidden;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

/* Sacred Geometry Background Pattern */
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 30%, var(--mystical-glow) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(100, 181, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, var(--mystical-glow) 0%, transparent 30%);
  pointer-events: none;
  z-index: -1;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Cosmic Header with Sacred Typography */
.app-header {
  text-align: center;
  padding: 3rem 2rem;
  background:
    linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%),
    radial-gradient(ellipse at center, var(--mystical-glow) 0%, transparent 70%);
  backdrop-filter: blur(15px);
  border-bottom: 2px solid var(--feather-gold);
  box-shadow: 0 4px 20px rgba(212, 175, 55, 0.2);
  position: relative;
}

.app-header::before {
  content: '✦';
  position: absolute;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.5rem;
  color: var(--feather-gold);
  opacity: 0.7;
}

.app-header h1 {
  font-family: var(--font-title);
  font-size: 3rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, var(--feather-gold) 0%, var(--bioluminescent-blue) 50%, var(--feather-gold) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px var(--mystical-glow);
  letter-spacing: 2px;
  position: relative;
}

.app-header p {
  font-family: var(--font-body);
  font-style: italic;
  font-size: 1.1rem;
  margin: 0;
  color: var(--moonlight-silver);
  opacity: 0.9;
  letter-spacing: 1px;
}

.app-main {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  position: relative;
}

/* Oracle Reading Sacred Container */
.oracle-reading {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

/* Question Phase - Sacred Meditation Space */
.question-phase {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  position: relative;
}

.question-phase h2 {
  font-family: var(--font-title);
  font-size: 2.5rem;
  font-weight: 500;
  margin-bottom: 2rem;
  color: var(--feather-gold);
  text-shadow: 0 0 20px var(--mystical-glow);
  letter-spacing: 1px;
}

.question-area {
  background:
    linear-gradient(135deg, rgba(74, 44, 90, 0.3) 0%, rgba(26, 26, 46, 0.5) 100%);
  padding: 3rem;
  border-radius: 20px;
  border: 2px solid var(--feather-gold);
  box-shadow:
    0 8px 32px rgba(212, 175, 55, 0.2),
    inset 0 1px 0 rgba(245, 243, 240, 0.1);
  margin-top: 2rem;
  backdrop-filter: blur(10px);
  position: relative;
}

.question-area::before {
  content: '🌙';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2rem;
  background: var(--cosmic-void);
  padding: 0 1rem;
}

.question-area p {
  font-family: var(--font-body);
  font-size: 1.2rem;
  line-height: 1.8;
  color: var(--moonlight-silver);
  margin-bottom: 2rem;
}

/* Sacred Buttons with Tribal Patterns */
.continue-button,
.shuffle-button,
.new-reading-button {
  font-family: var(--font-title);
  background:
    linear-gradient(135deg, var(--sacred-purple) 0%, var(--tribal-earth) 100%);
  color: var(--ethereal-white);
  border: 2px solid var(--feather-gold);
  border-radius: 15px;
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.4s ease;
  margin-top: 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.continue-button::before,
.shuffle-button::before,
.new-reading-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--mystical-glow), transparent);
  transition: left 0.6s ease;
}

.continue-button:hover,
.shuffle-button:hover,
.new-reading-button:hover {
  background:
    linear-gradient(135deg, var(--feather-gold) 0%, var(--bioluminescent-blue) 100%);
  color: var(--cosmic-void);
  box-shadow:
    0 0 30px var(--mystical-glow),
    0 8px 25px rgba(212, 175, 55, 0.4);
  transform: translateY(-3px);
}

.continue-button:hover::before,
.shuffle-button:hover::before,
.new-reading-button:hover::before {
  left: 100%;
}

/* Sacred Deck Styles */
.deck-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  position: relative;
  gap: 3rem;
}

.deck {
  position: relative;
  display: inline-block;
  cursor: pointer;
  filter: drop-shadow(0 8px 20px rgba(212, 175, 55, 0.3));
}

.deck-card {
  position: absolute;
  width: 140px;
  height: 210px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow:
    0 6px 20px rgba(10, 10, 15, 0.6),
    0 0 0 2px var(--feather-gold);
  border: 1px solid var(--mystical-glow);
}

.deck-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: sepia(20%) saturate(1.2) brightness(0.9);
}

/* Selection Phase - Sacred Choice */
.selection-phase {
  text-align: center;
  position: relative;
}

.selection-phase h2 {
  font-family: var(--font-title);
  color: var(--feather-gold);
  font-size: 2.5rem;
  margin-bottom: 3rem;
  text-shadow: 0 0 20px var(--mystical-glow);
}

.spread-options {
  display: flex;
  gap: 3rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 2rem;
}

.spread-option {
  background:
    linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%);
  border: 2px solid var(--feather-gold);
  border-radius: 20px;
  padding: 2.5rem;
  min-width: 250px;
  cursor: pointer;
  transition: all 0.4s ease;
  position: relative;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.2);
}

.spread-option::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--feather-gold), var(--bioluminescent-blue), var(--feather-gold));
  border-radius: 20px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.spread-option:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 15px 40px rgba(212, 175, 55, 0.4),
    0 0 30px var(--mystical-glow);
}

.spread-option:hover::before {
  opacity: 1;
}

.spread-option h3 {
  font-family: var(--font-title);
  color: var(--feather-gold);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  letter-spacing: 1px;
}

.spread-option p {
  color: var(--moonlight-silver);
  font-style: italic;
  line-height: 1.6;
}

/* Drawing Phase - Mystical Transition */
.drawing-phase {
  text-align: center;
  padding: 4rem 0;
  position: relative;
}

.drawing-phase h2 {
  font-family: var(--font-title);
  color: var(--feather-gold);
  font-size: 2.5rem;
  margin-bottom: 3rem;
  text-shadow: 0 0 20px var(--mystical-glow);
}

.drawing-animation {
  font-size: 5rem;
  animation: mysticalPulse 2s ease-in-out infinite;
}

@keyframes mysticalPulse {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: drop-shadow(0 0 20px var(--mystical-glow));
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    filter: drop-shadow(0 0 40px var(--feather-gold));
  }
}

/* Drawing Cards Phase */
.drawing-cards-phase {
  text-align: center;
}

.drawing-cards-phase h2 {
  font-family: var(--font-title);
  color: var(--feather-gold);
  font-size: 2.5rem;
  margin-bottom: 3rem;
  text-shadow: 0 0 20px var(--mystical-glow);
}

/* Reading Phase - Sacred Revelation */
.reading-phase {
  text-align: center;
  position: relative;
}

.reading-phase h2 {
  font-family: var(--font-title);
  color: var(--feather-gold);
  font-size: 2.5rem;
  margin-bottom: 3rem;
  text-shadow: 0 0 20px var(--mystical-glow);
}

.card-spread {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  min-height: 450px;
  margin: 3rem 0;
  flex-wrap: wrap;
}

/* Sacred Card Styles - Cosmic-Tribal Fusion */
.card-container {
  position: relative;
  margin: 1.5rem;
}

.card-label {
  text-align: center;
  font-family: var(--font-title);
  color: var(--feather-gold);
  font-weight: 500;
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  letter-spacing: 1px;
  text-shadow: 0 0 10px var(--mystical-glow);
}

.card {
  width: 220px;
  height: 330px;
  perspective: 1200px;
  cursor: pointer;
  position: relative;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 20px;
  overflow: hidden;
  box-shadow:
    0 12px 35px rgba(10, 10, 15, 0.6),
    0 0 0 3px var(--feather-gold),
    inset 0 1px 0 rgba(245, 243, 240, 0.1);
}

/* Card Back - Sacred Symbols */
.card-back {
  background:
    linear-gradient(135deg, var(--tribal-earth) 0%, var(--sacred-purple) 50%, var(--shadow-deep) 100%),
    radial-gradient(circle at center, var(--mystical-glow) 0%, transparent 70%);
  border: 3px solid var(--feather-gold);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.card-back::before {
  content: '✦ ◊ ✦';
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: var(--feather-gold);
  font-size: 1.2rem;
  letter-spacing: 8px;
}

.card-back::after {
  content: '🌙';
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 2rem;
  filter: drop-shadow(0 0 10px var(--mystical-glow));
}

.card-back img {
  width: 75%;
  height: 75%;
  object-fit: cover;
  border-radius: 15px;
  filter: sepia(30%) saturate(1.3) brightness(0.8) contrast(1.1);
  border: 2px solid var(--mystical-glow);
}

.card-click-hint {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  font-family: var(--font-body);
  color: var(--moonlight-silver);
  font-size: 0.9rem;
  font-style: italic;
  opacity: 0.9;
  text-shadow: 0 0 8px var(--mystical-glow);
  animation: gentlePulse 2s ease-in-out infinite;
}

@keyframes gentlePulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Card Front - Sacred Revelation */
.card-front {
  background:
    linear-gradient(135deg, var(--ethereal-white) 0%, var(--moonlight-silver) 100%),
    radial-gradient(circle at top right, var(--mystical-glow) 0%, transparent 50%);
  color: var(--cosmic-void);
  transform: rotateY(180deg);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  border: 2px solid var(--feather-gold);
}

.card-front::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, var(--mystical-glow) 0%, transparent 30%),
    radial-gradient(circle at 80% 80%, rgba(100, 181, 246, 0.1) 0%, transparent 30%);
  pointer-events: none;
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.card-title {
  font-family: var(--font-title);
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--cosmic-void);
  margin-bottom: 0.8rem;
  text-align: center;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(212, 175, 55, 0.3);
}

.card-suite {
  font-family: var(--font-body);
  font-size: 1rem;
  color: var(--sacred-purple);
  text-align: center;
  margin-bottom: 1.5rem;
  font-style: italic;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.card-meaning {
  flex: 1;
  font-family: var(--font-body);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--shadow-deep);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
}

.card-affirmation {
  font-family: var(--font-body);
  font-size: 0.95rem;
  color: var(--tribal-earth);
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 2px solid var(--feather-gold);
  font-style: italic;
  line-height: 1.5;
  position: relative;
}

.card-affirmation::before {
  content: '✦';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--ethereal-white);
  color: var(--feather-gold);
  padding: 0 0.5rem;
  font-size: 0.8rem;
}

/* Reading Complete - Sacred Closure */
.reading-complete {
  margin-top: 4rem;
  padding: 3rem;
  background:
    linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%);
  border-radius: 20px;
  border: 2px solid var(--feather-gold);
  box-shadow:
    0 8px 32px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(245, 243, 240, 0.1);
  backdrop-filter: blur(15px);
  text-align: center;
  position: relative;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  z-index: 10;
}

.reading-complete::before {
  content: '✦ ◊ ✦';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--cosmic-void);
  color: var(--feather-gold);
  padding: 0 1rem;
  font-size: 1.2rem;
  letter-spacing: 8px;
}

.reading-complete p {
  font-family: var(--font-body);
  font-size: 1.2rem;
  color: var(--moonlight-silver);
  margin-bottom: 2rem;
  line-height: 1.7;
  font-style: italic;
}

/* Responsive Design - Sacred Adaptability */
@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2.2rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .question-phase h2,
  .selection-phase h2,
  .reading-phase h2 {
    font-size: 2rem;
  }

  .spread-options {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }

  .spread-option {
    min-width: 200px;
    padding: 2rem;
  }

  .card-spread {
    flex-direction: column;
    gap: 2rem;
    align-items: center;
  }

  .card {
    width: 180px;
    height: 270px;
  }

  .card-container {
    margin: 1rem;
  }

  .question-area,
  .reading-complete {
    padding: 2rem;
    margin: 1rem;
  }

  .deck-card {
    width: 120px;
    height: 180px;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 2rem 1rem;
  }

  .app-header h1 {
    font-size: 1.8rem;
  }

  .app-main {
    padding: 1rem;
  }

  .card {
    width: 160px;
    height: 240px;
  }

  .question-area,
  .reading-complete {
    padding: 1.5rem;
  }

  .spread-option {
    min-width: 180px;
    padding: 1.5rem;
  }
}
