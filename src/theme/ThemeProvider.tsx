import React from 'react';
import { ThemeProvider as MuiThemeProvider, CssBaseline } from '@mui/material';
import { fallingBirdTheme } from './muiTheme';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  return (
    <MuiThemeProvider theme={fallingBirdTheme}>
      <CssBaseline />
      {children}
    </MuiThemeProvider>
  );
};
