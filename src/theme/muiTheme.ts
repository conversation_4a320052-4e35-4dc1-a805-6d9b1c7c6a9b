import { createTheme, ThemeOptions } from '@mui/material/styles';

// Descent of Falling Bird - Cosmic-Tribal Fusion Color Palette
const colors = {
  // Primary cosmic-tribal colors
  cosmicVoid: '#0a0a0f',
  tribalEarth: '#8b4513',
  sacredPurple: '#4a2c5a',
  featherGold: '#d4af37',
  shadowDeep: '#1a1a2e',
  
  // Mystical accent colors
  bioluminescentBlue: '#64b5f6',
  mysticalGlow: 'rgba(212, 175, 55, 0.4)',
  moonlightSilver: '#c0c0c0',
  etherealWhite: '#f5f3f0',
  
  // Gradients and effects
  cosmicGradient: 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #4a2c5a 100%)',
  tribalGradient: 'linear-gradient(135deg, #8b4513 0%, #d4af37 100%)',
  mysticalGradient: 'linear-gradient(135deg, #4a2c5a 0%, #64b5f6 100%)',
};

// Typography configuration preserving existing fonts
const typography = {
  fontFamily: '"<PERSON>ra", "E<PERSON> Garamond", serif', // Body text
  h1: {
    fontFamily: '"C<PERSON><PERSON>", "Cormorant Garamond", serif', // Titles
    fontWeight: 500,
    fontSize: '3rem',
    letterSpacing: '2px',
    textShadow: `0 0 20px ${colors.mysticalGlow}`,
  },
  h2: {
    fontFamily: '"Cinzel", "Cormorant Garamond", serif',
    fontWeight: 500,
    fontSize: '2.5rem',
    letterSpacing: '1px',
    textShadow: `0 0 20px ${colors.mysticalGlow}`,
  },
  h3: {
    fontFamily: '"Cinzel", "Cormorant Garamond", serif',
    fontWeight: 500,
    fontSize: '1.8rem',
    letterSpacing: '1px',
  },
  h4: {
    fontFamily: '"Cinzel", "Cormorant Garamond", serif',
    fontWeight: 500,
    fontSize: '1.5rem',
  },
  h5: {
    fontFamily: '"Cinzel", "Cormorant Garamond", serif',
    fontWeight: 500,
    fontSize: '1.2rem',
  },
  h6: {
    fontFamily: '"Cinzel", "Cormorant Garamond", serif',
    fontWeight: 500,
    fontSize: '1rem',
  },
  body1: {
    fontFamily: '"Lora", "EB Garamond", serif',
    fontSize: '1rem',
    lineHeight: 1.6,
  },
  body2: {
    fontFamily: '"Lora", "EB Garamond", serif',
    fontSize: '0.95rem',
    lineHeight: 1.5,
  },
  button: {
    fontFamily: '"Cinzel", "Cormorant Garamond", serif',
    fontWeight: 500,
    letterSpacing: '1px',
    textTransform: 'none' as const,
  },
};

// Create the theme configuration
const themeOptions: ThemeOptions = {
  palette: {
    mode: 'dark',
    primary: {
      main: colors.featherGold,
      light: colors.moonlightSilver,
      dark: colors.tribalEarth,
      contrastText: colors.cosmicVoid,
    },
    secondary: {
      main: colors.sacredPurple,
      light: colors.bioluminescentBlue,
      dark: colors.shadowDeep,
      contrastText: colors.etherealWhite,
    },
    background: {
      default: colors.cosmicVoid,
      paper: colors.shadowDeep,
    },
    text: {
      primary: colors.etherealWhite,
      secondary: colors.moonlightSilver,
    },
    error: {
      main: '#ff6b6b',
    },
    warning: {
      main: colors.featherGold,
    },
    info: {
      main: colors.bioluminescentBlue,
    },
    success: {
      main: '#51cf66',
    },
  },
  typography,
  shape: {
    borderRadius: 20,
  },
  spacing: 8,
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          background: `linear-gradient(135deg, ${colors.sacredPurple} 0%, ${colors.tribalEarth} 100%)`,
          color: colors.etherealWhite,
          border: `2px solid ${colors.featherGold}`,
          borderRadius: '15px',
          padding: '12px 30px',
          fontSize: '1.1rem',
          fontWeight: 500,
          letterSpacing: '1px',
          textTransform: 'none',
          position: 'relative',
          overflow: 'hidden',
          boxShadow: `0 4px 15px ${colors.mysticalGlow}`,
          transition: 'all 0.4s ease',
          '&:hover': {
            background: `linear-gradient(135deg, ${colors.featherGold} 0%, ${colors.bioluminescentBlue} 100%)`,
            color: colors.cosmicVoid,
            boxShadow: `0 0 30px ${colors.mysticalGlow}, 0 8px 25px ${colors.mysticalGlow}`,
            transform: 'translateY(-3px)',
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: '-100%',
            width: '100%',
            height: '100%',
            background: `linear-gradient(90deg, transparent, ${colors.mysticalGlow}, transparent)`,
            transition: 'left 0.6s ease',
          },
          '&:hover::before': {
            left: '100%',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
          border: `2px solid ${colors.featherGold}`,
          borderRadius: '20px',
          boxShadow: `0 8px 32px ${colors.mysticalGlow}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
          backdropFilter: 'blur(15px)',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
          border: `2px solid ${colors.featherGold}`,
          borderRadius: '20px',
          boxShadow: `0 8px 32px ${colors.mysticalGlow}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
          backdropFilter: 'blur(15px)',
        },
      },
    },
    MuiContainer: {
      styleOverrides: {
        root: {
          position: 'relative',
        },
      },
    },
  },
};

// Create and export the theme
export const fallingBirdTheme = createTheme(themeOptions);

// Export colors for use in custom components
export { colors };
