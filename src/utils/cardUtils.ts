import { OracleCard, DrawnCard, SpreadType } from '../types/Card';

export const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

export const drawCards = (
  deck: OracleCard[], 
  count: number
): DrawnCard[] => {
  const shuffledDeck = shuffleArray(deck);
  return shuffledDeck.slice(0, count).map((card, index) => ({
    ...card,
    id: `card-${Date.now()}-${index}`,
    isRevealed: false,
    position: index
  }));
};

export const getCardCount = (spreadType: SpreadType): number => {
  return spreadType === 'single' ? 1 : 3;
};

export const getSpreadPositions = (spreadType: SpreadType) => {
  if (spreadType === 'single') {
    return [{ x: 0, y: 0, label: 'Your Card' }];
  }
  
  return [
    { x: -120, y: 0, label: 'Past' },
    { x: 0, y: 0, label: 'Present' },
    { x: 120, y: 0, label: 'Future' }
  ];
};

export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};
