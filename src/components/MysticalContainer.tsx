import React from 'react';
import { Container, ContainerProps, Paper, PaperProps } from '@mui/material';
import { motion } from 'framer-motion';
import { styled } from '@mui/material/styles';
import { colors } from '../theme/muiTheme';

// Styled MUI Container with mystical effects
const StyledMysticalContainer = styled(Container)(() => ({
  position: 'relative',
  padding: '32px',
  
  // Add mystical background effects
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 20% 20%, ${colors.mysticalGlow} 0%, transparent 30%),
      radial-gradient(circle at 80% 80%, rgba(100, 181, 246, 0.1) 0%, transparent 30%)
    `,
    pointerEvents: 'none',
    zIndex: 0,
  },
  
  // Ensure content is above background effects
  '& > *': {
    position: 'relative',
    zIndex: 1,
  },
}));

// Styled MUI Paper with mystical card effects
const StyledMysticalPaper = styled(Paper)(() => ({
  background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
  border: `2px solid ${colors.featherGold}`,
  borderRadius: '20px',
  boxShadow: `0 8px 32px ${colors.mysticalGlow}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
  backdropFilter: 'blur(15px)',
  position: 'relative',
  
  '&::before': {
    content: '"✦ ◊ ✦"',
    position: 'absolute',
    top: '-15px',
    left: '50%',
    transform: 'translateX(-50%)',
    background: colors.cosmicVoid,
    color: colors.featherGold,
    padding: '0 1rem',
    fontSize: '1.2rem',
    letterSpacing: '8px',
    zIndex: 2,
  },
}));

interface MysticalContainerProps extends ContainerProps {
  children: React.ReactNode;
  animate?: any;
  initial?: any;
  exit?: any;
  transition?: any;
}

interface MysticalPaperProps extends PaperProps {
  children: React.ReactNode;
  animate?: any;
  initial?: any;
  exit?: any;
  transition?: any;
  showDecorator?: boolean;
  whileHover?: any;
  whileTap?: any;
}

export const MysticalContainer: React.FC<MysticalContainerProps> = ({
  children,
  animate,
  initial,
  exit,
  transition,
  ...containerProps
}) => {
  const MotionContainer = motion(StyledMysticalContainer);

  return (
    <MotionContainer
      animate={animate}
      initial={initial}
      exit={exit}
      transition={transition}
      {...(containerProps as any)}
    >
      {children}
    </MotionContainer>
  );
};

export const MysticalPaper: React.FC<MysticalPaperProps> = ({
  children,
  animate,
  initial,
  exit,
  transition,
  showDecorator = true,
  whileHover,
  whileTap,
  ...paperProps
}) => {
  const StyledPaper = showDecorator ? StyledMysticalPaper : Paper;
  const Component = motion(StyledPaper);

  return (
    <Component
      animate={animate}
      initial={initial}
      exit={exit}
      transition={transition}
      whileHover={whileHover}
      whileTap={whileTap}
      {...(paperProps as any)}
      sx={{
        ...(!showDecorator && {
          background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
          border: `2px solid ${colors.featherGold}`,
          borderRadius: '20px',
          boxShadow: `0 8px 32px ${colors.mysticalGlow}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
          backdropFilter: 'blur(15px)',
          '&::before': {
            display: 'none',
          },
        }),
        ...paperProps.sx,
      }}
    >
      {children}
    </Component>
  );
};
