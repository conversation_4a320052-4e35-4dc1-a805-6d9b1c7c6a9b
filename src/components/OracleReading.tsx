import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Typography, Box } from '@mui/material';
import { OracleCard, ReadingState, SpreadType } from '../types/Card';
import { drawCards, getCardCount, getSpreadPositions, sleep } from '../utils/cardUtils';
import { Deck } from './Deck';
import { Card } from './Card';
import { MysticalButton } from './MysticalButton';
import { MysticalContainer, MysticalPaper } from './MysticalContainer';
import oracleCardsData from '../assets/falling_bird_oracle_cards.json';

export const OracleReading: React.FC = () => {
  const [cards] = useState<OracleCard[]>(oracleCardsData);
  const [readingState, setReadingState] = useState<ReadingState>({
    phase: 'question',
    spreadType: null,
    drawnCards: [],
    isShuffling: false,
    deckShuffled: false
  });

  const handleShuffle = async () => {
    setReadingState(prev => ({ ...prev, isShuffling: true }));
    await sleep(2000); // Shuffle animation duration
    setReadingState(prev => ({ 
      ...prev, 
      isShuffling: false, 
      deckShuffled: true,
      phase: 'selection'
    }));
  };

  const handleSpreadSelection = (spreadType: SpreadType) => {
    setReadingState(prev => ({ ...prev, spreadType, phase: 'drawing' }));

    // Draw cards after a brief delay
    setTimeout(() => {
      const cardCount = getCardCount(spreadType);
      const drawnCards = drawCards(cards, cardCount);
      setReadingState(prev => ({
        ...prev,
        drawnCards,
        phase: 'drawing-cards'
      }));

      // After drawing animation, switch to reading phase
      setTimeout(() => {
        setReadingState(prev => ({ ...prev, phase: 'reading' }));
      }, 1500); // Allow time for drawing animation
    }, 500);
  };

  const handleCardReveal = (cardId: string) => {
    setReadingState(prev => ({
      ...prev,
      drawnCards: prev.drawnCards.map(card =>
        card.id === cardId ? { ...card, isRevealed: true } : card
      )
    }));

    // Check if all cards are revealed
    const allRevealed = readingState.drawnCards.every(card => 
      card.id === cardId || card.isRevealed
    );
    
    if (allRevealed) {
      setTimeout(() => {
        setReadingState(prev => ({ ...prev, phase: 'complete' }));
      }, 1000);
    }
  };

  const resetReading = () => {
    setReadingState({
      phase: 'question',
      spreadType: null,
      drawnCards: [],
      isShuffling: false,
      deckShuffled: false
    });
  };

  const renderPhase = () => {
    switch (readingState.phase) {
      case 'question':
        return (
          <MysticalContainer
            className="question-phase"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            maxWidth="md"
          >
            <Typography variant="h2" component="h2" align="center" sx={{ mb: 3, color: 'primary.main' }}>
              Ask Your Question
            </Typography>
            <Typography variant="body1" align="center" sx={{ mb: 4, color: 'text.secondary', fontStyle: 'italic' }}>
              Take a moment to center yourself and focus on your question.
              What guidance do you seek from the oracle?
            </Typography>
            <Box className="question-area" sx={{ textAlign: 'center' }}>
              <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary', fontStyle: 'italic' }}>
                Hold your question in your mind and heart...
              </Typography>
              <MysticalButton
                onClick={() => setReadingState(prev => ({ ...prev, phase: 'shuffle' }))}
                size="large"
              >
                I'm Ready
              </MysticalButton>
            </Box>
          </MysticalContainer>
        );

      case 'shuffle':
        return (
          <MysticalContainer
            className="shuffle-phase"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            maxWidth="md"
          >
            <Deck
              cardCount={cards.length}
              isShuffling={readingState.isShuffling}
              onShuffle={handleShuffle}
              canShuffle={!readingState.isShuffling}
            />
          </MysticalContainer>
        );

      case 'selection':
        return (
          <MysticalContainer
            className="selection-phase"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            maxWidth="lg"
          >
            <Typography variant="h2" component="h2" align="center" sx={{ mb: 4, color: 'primary.main' }}>
              Choose Your Spread
            </Typography>
            <Box className="spread-options" sx={{ display: 'flex', gap: 4, justifyContent: 'center', flexWrap: 'wrap' }}>
              <MysticalPaper
                className="spread-option"
                onClick={() => handleSpreadSelection('single')}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                showDecorator={false}
                sx={{
                  p: 4,
                  minWidth: 250,
                  cursor: 'pointer',
                  textAlign: 'center',
                  transition: 'all 0.4s ease',
                }}
              >
                <Typography variant="h5" component="h3" sx={{ mb: 2, color: 'primary.main' }}>
                  Single Card
                </Typography>
                <Typography variant="body1" sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                  One card for focused guidance
                </Typography>
              </MysticalPaper>

              <MysticalPaper
                className="spread-option"
                onClick={() => handleSpreadSelection('three')}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                showDecorator={false}
                sx={{
                  p: 4,
                  minWidth: 250,
                  cursor: 'pointer',
                  textAlign: 'center',
                  transition: 'all 0.4s ease',
                }}
              >
                <Typography variant="h5" component="h3" sx={{ mb: 2, color: 'primary.main' }}>
                  Three Card Spread
                </Typography>
                <Typography variant="body1" sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                  Past, Present, Future
                </Typography>
              </MysticalPaper>
            </Box>
          </MysticalContainer>
        );

      case 'drawing':
        return (
          <MysticalContainer
            className="drawing-phase"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            maxWidth="md"
          >
            <Typography variant="h2" component="h2" align="center" sx={{ mb: 4, color: 'primary.main' }}>
              Drawing Your Cards...
            </Typography>
            <Box className="drawing-animation" sx={{ textAlign: 'center', fontSize: '5rem' }}>
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1.1, rotate: 360 }}
                transition={{ duration: 1, ease: "easeInOut" }}
              >
                ✨
              </motion.div>
            </Box>
          </MysticalContainer>
        );

      case 'drawing-cards':
        const drawingPositions = getSpreadPositions(readingState.spreadType!);
        return (
          <MysticalContainer
            className="drawing-cards-phase"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            maxWidth="lg"
          >
            <Typography variant="h2" component="h2" align="center" sx={{ mb: 4, color: 'primary.main' }}>
              Your Cards
            </Typography>
            <div className="card-spread">
              {readingState.drawnCards.map((card, index) => (
                <Card
                  key={card.id}
                  card={card}
                  onReveal={handleCardReveal}
                  position={drawingPositions[index]}
                  isDrawing={true}
                />
              ))}
            </div>
          </MysticalContainer>
        );

      case 'reading':
      case 'complete':
        const positions = getSpreadPositions(readingState.spreadType!);
        return (
          <MysticalContainer
            className="reading-phase"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            maxWidth="lg"
          >
            <Typography variant="h2" component="h2" align="center" sx={{ mb: 4, color: 'primary.main' }}>
              Your Reading
            </Typography>
            <div className="card-spread">
              {readingState.drawnCards.map((card, index) => (
                <Card
                  key={card.id}
                  card={card}
                  onReveal={handleCardReveal}
                  position={positions[index]}
                  isDrawing={false}
                />
              ))}
            </div>

            {readingState.phase === 'complete' && (
              <MysticalPaper
                className="reading-complete"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                sx={{
                  mt: 4,
                  p: 3,
                  textAlign: 'center',
                  maxWidth: 600,
                  mx: 'auto',
                }}
              >
                <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary', fontStyle: 'italic', fontSize: '1.2rem' }}>
                  Your reading is complete. Take time to reflect on the messages.
                </Typography>
                <MysticalButton
                  onClick={resetReading}
                  size="large"
                >
                  New Reading
                </MysticalButton>
              </MysticalPaper>
            )}
          </MysticalContainer>
        );

      default:
        return null;
    }
  };

  return (
    <div className="oracle-reading">
      <AnimatePresence mode="wait">
        {renderPhase()}
      </AnimatePresence>
    </div>
  );
};
